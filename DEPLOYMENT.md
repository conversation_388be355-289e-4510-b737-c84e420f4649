# 部署指南

## 部署前检查清单

### 1. 环境配置
- [ ] 配置 `.env.local` 文件
- [ ] 设置正确的数据库连接信息
- [ ] 生成安全的 JWT_SECRET
- [ ] 配置邮件服务（如需要）

### 2. 数据库设置
- [ ] 创建 MySQL 数据库
- [ ] 执行 `database/init.sql` 初始化脚本
- [ ] 验证管理员账号创建成功
- [ ] 测试数据库连接：`npm run test:db`

### 3. 功能测试
- [ ] 多语言切换功能
- [ ] 用户注册流程
- [ ] 用户登录功能
- [ ] 咨询表单提交
- [ ] 管理员登录
- [ ] 用户管理功能
- [ ] 通知管理功能

### 4. 安全检查
- [ ] 更改默认管理员密码
- [ ] 设置强密码策略
- [ ] 验证 JWT token 安全性
- [ ] 检查 API 权限控制

## 本地开发环境设置

1. **安装依赖**
   ```bash
   npm install
   ```

2. **配置环境变量**
   ```bash
   cp .env.local.example .env.local
   # 编辑 .env.local 文件
   ```

3. **设置数据库**
   ```bash
   # 创建数据库
   mysql -u root -p -e "CREATE DATABASE jpcomserver CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
   
   # 导入初始化脚本
   mysql -u root -p jpcomserver < database/init.sql
   ```

4. **测试数据库连接**
   ```bash
   npm run test:db
   ```

5. **启动开发服务器**
   ```bash
   npm run dev
   ```

## 生产环境部署

### Vercel 部署

1. **推送代码到 GitHub**
   ```bash
   git add .
   git commit -m "Initial commit"
   git push origin main
   ```

2. **在 Vercel 中导入项目**
   - 访问 [vercel.com](https://vercel.com)
   - 点击 "New Project"
   - 导入 GitHub 仓库

3. **配置环境变量**
   在 Vercel 项目设置中添加以下环境变量：
   ```
   DB_HOST=your-database-host
   DB_PORT=3306
   DB_USER=your-database-user
   DB_PASSWORD=your-database-password
   DB_NAME=jpcomserver
   JWT_SECRET=your-super-secret-jwt-key
   JWT_EXPIRES_IN=7d
   NODE_ENV=production
   ```

4. **部署**
   - Vercel 会自动构建和部署
   - 检查部署日志确保无错误

### 其他平台部署

1. **构建项目**
   ```bash
   npm run build
   ```

2. **启动生产服务器**
   ```bash
   npm start
   ```

## 数据库迁移

如果需要更新数据库结构，创建迁移脚本：

```sql
-- migration_001.sql
ALTER TABLE users ADD COLUMN new_field VARCHAR(255);
```

## 监控和维护

### 日志监控
- 检查应用日志
- 监控数据库连接
- 关注错误率

### 性能优化
- 数据库查询优化
- 图片压缩
- CDN 配置

### 安全维护
- 定期更新依赖包
- 监控安全漏洞
- 备份数据库

## 故障排除

### 常见问题

1. **数据库连接失败**
   - 检查数据库服务是否运行
   - 验证连接参数
   - 检查防火墙设置

2. **JWT token 错误**
   - 确认 JWT_SECRET 配置
   - 检查 token 过期时间
   - 验证 token 格式

3. **多语言不工作**
   - 检查 middleware.ts 配置
   - 验证语言文件路径
   - 确认路由配置

4. **权限问题**
   - 检查用户角色设置
   - 验证 API 权限控制
   - 确认管理员账号

## 备份策略

### 数据库备份
```bash
# 每日备份
mysqldump -u root -p jpcomserver > backup_$(date +%Y%m%d).sql

# 恢复备份
mysql -u root -p jpcomserver < backup_20250619.sql
```

### 代码备份
- 使用 Git 版本控制
- 定期推送到远程仓库
- 标记重要版本

## 联系信息

如有问题，请联系开发团队。
