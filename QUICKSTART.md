# 快速启动指南

## 🚀 5分钟快速启动

### 1. 安装依赖
```bash
npm install
```

### 2. 配置数据库（可选 - 用于完整功能）
如果你有MySQL数据库，可以配置完整功能：

```bash
# 创建数据库
mysql -u root -p -e "CREATE DATABASE jpcomserver CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

# 导入初始化脚本
mysql -u root -p jpcomserver < database/init.sql

# 配置环境变量
cp .env.local .env.local.backup
# 编辑 .env.local 文件，设置你的数据库连接信息
```

### 3. 启动开发服务器
```bash
npm run dev
```

### 4. 访问应用
打开浏览器访问: http://localhost:3000

## 🎯 无数据库快速体验

如果你暂时没有MySQL数据库，也可以体验前端功能：

1. 启动项目：`npm run dev`
2. 访问 http://localhost:3000
3. 可以体验：
   - 多语言切换（右上角）
   - 界面浏览
   - 表单填写（提交会失败，但可以看到界面）

## 📋 默认账号（需要数据库）

- **管理员邮箱**: <EMAIL>
- **管理员密码**: admin123

## 🔧 功能测试清单

### 基础功能
- [ ] 访问主页 http://localhost:3000
- [ ] 测试语言切换（右上角 🇨🇳/🇯🇵 按钮）
- [ ] 浏览不同标签页（首页、赴日介护、赴日护士、关于我们）

### 用户功能（需要数据库）
- [ ] 点击"我要咨询"填写表单
- [ ] 提交咨询信息（会自动注册账号）
- [ ] 使用注册的邮箱和密码登录
- [ ] 查看个人中心
- [ ] 修改密码

### 管理员功能（需要数据库）
- [ ] 使用管理员账号登录
- [ ] 查看用户列表
- [ ] 查看用户详情
- [ ] 管理通知内容
- [ ] 用户状态管理

## 🐛 常见问题

### 1. 端口被占用
```bash
# 使用其他端口
npm run dev -- -p 3001
```

### 2. 数据库连接失败
- 检查MySQL服务是否运行
- 验证 .env.local 中的数据库配置
- 运行测试：`npm run test:db`

### 3. 依赖安装失败
```bash
# 清除缓存重新安装
rm -rf node_modules package-lock.json
npm install
```

### 4. 编译错误
```bash
# 检查TypeScript错误
npm run lint
```

## 📱 移动端测试

项目采用移动优先设计，建议在手机或开发者工具的移动端模式下测试：

1. 打开浏览器开发者工具（F12）
2. 切换到移动设备模式
3. 选择手机尺寸（如iPhone 12）
4. 测试各项功能

## 🌐 多语言测试

1. 点击右上角的语言切换按钮
2. 验证页面内容是否正确切换
3. 测试表单提交在不同语言下的表现
4. 检查错误信息是否正确显示

## 📊 性能检查

```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 🔗 相关链接

- [完整部署指南](./DEPLOYMENT.md)
- [项目文档](./README.md)
- [Next.js 文档](https://nextjs.org/docs)
- [next-intl 文档](https://next-intl-docs.vercel.app/)

## 💡 开发提示

- 修改语言文件：`messages/zh.json` 和 `messages/ja.json`
- 添加新页面：在 `src/app/[locale]/` 下创建
- 修改样式：使用 Tailwind CSS 类名
- API 接口：在 `src/app/api/` 下添加

祝你使用愉快！🎉
