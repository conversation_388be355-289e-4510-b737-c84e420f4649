# 日本人才交流平台 (JP Talent Exchange Platform)

一个专业的赴日工作服务平台，支持中文/日语双语，提供赴日介护、护士等工作机会的咨询和管理服务。

## 功能特性

- 🌐 **多语言支持**: 中文/日语双语切换
- 👥 **用户管理**: 用户注册、登录、个人中心
- 📝 **咨询服务**: 在线咨询表单提交
- 🔐 **权限管理**: 用户/管理员角色分离
- 📢 **通知系统**: 管理员可管理通知内容
- 📱 **移动优先**: 响应式设计，移动端友好
- 🛡️ **安全认证**: JWT token认证，密码加密

## 技术栈

- **前端**: Next.js 15, React 19, TypeScript, Tailwind CSS
- **国际化**: next-intl
- **后端**: Next.js API Routes
- **数据库**: MySQL
- **认证**: JWT, bcryptjs
- **部署**: 支持 Vercel 部署

## 快速开始

### 1. 环境要求

- Node.js 18+
- MySQL 8.0+
- npm 或 yarn

### 2. 安装依赖

```bash
npm install
```

### 3. 数据库设置

1. 创建 MySQL 数据库
2. 执行数据库初始化脚本：

```bash
mysql -u root -p < database/init.sql
```

### 4. 环境配置

复制 `.env.local` 文件并配置数据库连接：

```bash
# Database Configuration
DB_HOST=localhost
DB_PORT=3306
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=jpcomserver

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
```

### 5. 运行项目

```bash
npm run dev
```

访问 [http://localhost:3000](http://localhost:3000) 查看应用。

## 默认管理员账号

- **邮箱**: <EMAIL>
- **密码**: admin123

## 项目结构

```
src/
├── app/
│   ├── [locale]/              # 国际化路由
│   │   ├── page.tsx          # 主页
│   │   ├── consultation/     # 咨询页面
│   │   ├── login/           # 登录页面
│   │   ├── profile/         # 用户个人中心
│   │   └── admin/           # 管理员界面
│   └── api/                 # API 路由
│       ├── auth/            # 认证相关 API
│       ├── notifications/   # 通知管理 API
│       └── admin/          # 管理员 API
├── components/              # 可复用组件
├── lib/                    # 工具库
│   ├── db.ts              # 数据库连接
│   └── auth.ts            # 认证工具
└── middleware.ts          # 国际化中间件

messages/                  # 国际化语言文件
├── zh.json               # 中文
└── ja.json               # 日语

database/
└── init.sql              # 数据库初始化脚本
```

## 主要功能

### 用户功能
- 多语言咨询表单提交
- 用户注册和登录
- 个人信息管理
- 密码修改
- 咨询记录查看

### 管理员功能
- 用户列表管理
- 用户详情查看
- 用户状态管理（启用/禁用）
- 通知内容管理（增删改）
- 咨询记录备注

### 系统功能
- 中文/日语语言切换
- 响应式设计
- 安全认证
- 数据持久化

## API 接口

### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/me` - 获取当前用户信息
- `POST /api/auth/change-password` - 修改密码

### 通知管理
- `GET /api/notifications` - 获取通知列表
- `POST /api/notifications` - 创建通知（管理员）
- `PUT /api/notifications/[id]` - 更新通知（管理员）
- `DELETE /api/notifications/[id]` - 删除通知（管理员）

### 用户管理（管理员）
- `GET /api/admin/users` - 获取用户列表
- `GET /api/admin/users/[id]` - 获取用户详情
- `PUT /api/admin/users/[id]` - 更新用户状态

## 部署

### Vercel 部署

1. 推送代码到 GitHub
2. 在 Vercel 中导入项目
3. 配置环境变量
4. 部署

### 其他平台部署

```bash
npm run build
npm start
```

## 开发说明

- 使用 TypeScript 进行类型安全开发
- 遵循 Next.js App Router 规范
- 使用 Tailwind CSS 进行样式开发
- API 路由使用 RESTful 设计
- 数据库操作使用参数化查询防止 SQL 注入

## 许可证

MIT License
