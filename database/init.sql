-- 创建数据库
CREATE DATABASE IF NOT EXISTS jpcomserver CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE jpcomserver;

-- 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    nickname <PERSON><PERSON><PERSON><PERSON>(100),
    real_name <PERSON><PERSON><PERSON><PERSON>(100),
    gender ENUM('male', 'female'),
    birthday DATE,
    hometown VARCHAR(100),
    education ENUM('below_secondary', 'college', 'bachelor', 'graduate'),
    job_intention ENUM('care', 'nurse'),
    work_experience ENUM('none', '0-1', '1-2', '2-3', '3+'),
    japanese_level ENUM('none', 'N5', 'N4', 'N3', 'N2', 'N1'),
    phone VARCHAR(20),
    wechat VARCHAR(100),
    role <PERSON><PERSON><PERSON>('user', 'admin') DEFAULT 'user',
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_email (email),
    INDEX idx_role (role),
    INDEX idx_created_at (created_at)
);

-- 通知表
CREATE TABLE notifications (
    id INT AUTO_INCREMENT PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL,
    INDEX idx_is_active (is_active),
    INDEX idx_created_at (created_at)
);

-- 咨询记录表（用于记录用户提交的咨询信息历史）
CREATE TABLE consultations (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    nickname VARCHAR(100),
    real_name VARCHAR(100),
    gender ENUM('male', 'female'),
    birthday DATE,
    hometown VARCHAR(100),
    education ENUM('below_secondary', 'college', 'bachelor', 'graduate'),
    job_intention ENUM('care', 'nurse'),
    work_experience ENUM('none', '0-1', '1-2', '2-3', '3+'),
    japanese_level ENUM('none', 'N5', 'N4', 'N3', 'N2', 'N1'),
    phone VARCHAR(20),
    wechat VARCHAR(100),
    status ENUM('pending', 'processing', 'completed', 'cancelled') DEFAULT 'pending',
    notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at)
);

-- 管理员操作日志表
CREATE TABLE admin_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    admin_id INT NOT NULL,
    action VARCHAR(100) NOT NULL,
    target_type VARCHAR(50),
    target_id INT,
    description TEXT,
    ip_address VARCHAR(45),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (admin_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_admin_id (admin_id),
    INDEX idx_action (action),
    INDEX idx_created_at (created_at)
);

-- 插入默认管理员账号（密码：admin123）
INSERT INTO users (
    email, 
    password, 
    nickname, 
    real_name, 
    role, 
    is_active
) VALUES (
    '<EMAIL>',
    '$2b$10$rOzJqKqKqKqKqKqKqKqKqOzJqKqKqKqKqKqKqKqKqKqKqKqKqKqKq',
    '系统管理员',
    '管理员',
    'admin',
    TRUE
);

-- 插入一些示例通知
INSERT INTO notifications (title, content, created_by) VALUES 
('欢迎使用', '欢迎使用日本人才交流平台！我们致力于为您提供最优质的服务。', 1),
('系统维护通知', '系统将于本周末进行维护升级，届时可能会有短暂的服务中断，敬请谅解。', 1),
('新功能上线', '个人中心功能已上线，用户可以查看和管理自己的咨询记录。', 1);
