const mysql = require('mysql2/promise');
require('dotenv').config({ path: '.env.local' });

async function testDatabaseConnection() {
  const dbConfig = {
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '3306'),
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'jpcomserver',
  };

  console.log('Testing database connection...');
  console.log('Config:', {
    ...dbConfig,
    password: '***' // Hide password in logs
  });

  try {
    const connection = await mysql.createConnection(dbConfig);
    console.log('✅ Database connection successful!');
    
    // Test basic query
    const [rows] = await connection.execute('SELECT COUNT(*) as count FROM users');
    console.log(`✅ Found ${rows[0].count} users in database`);
    
    // Test notifications
    const [notifications] = await connection.execute('SELECT COUNT(*) as count FROM notifications');
    console.log(`✅ Found ${notifications[0].count} notifications in database`);
    
    await connection.end();
    console.log('✅ Database test completed successfully!');
    
  } catch (error) {
    console.error('❌ Database connection failed:', error.message);
    process.exit(1);
  }
}

testDatabaseConnection();
