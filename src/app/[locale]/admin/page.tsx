'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';

interface User {
  id: number;
  email: string;
  nickname?: string;
  real_name?: string;
  gender?: string;
  job_intention?: string;
  is_active: boolean;
  created_at: string;
}

interface Notification {
  id: number;
  title: string;
  content: string;
  is_active: boolean;
  created_at: string;
  created_by_name?: string;
}

export default function AdminPage() {
  const router = useRouter();
  const t = useTranslations();
  const locale = useLocale();
  
  const [activeTab, setActiveTab] = useState('users');
  const [users, setUsers] = useState<User[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  
  // 通知表单状态
  const [notificationForm, setNotificationForm] = useState({
    id: null as number | null,
    title: '',
    content: '',
    is_active: true
  });
  const [showNotificationForm, setShowNotificationForm] = useState(false);
  const [isSubmittingNotification, setIsSubmittingNotification] = useState(false);

  useEffect(() => {
    checkAdminAuth();
    fetchUsers();
    fetchNotifications();
  }, []);

  const checkAdminAuth = () => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (!token || !userData) {
      router.push(`/${locale}/login`);
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      if (parsedUser.role !== 'admin') {
        router.push(`/${locale}/profile`);
        return;
      }
    } catch (error) {
      console.error('解析用户数据失败:', error);
      router.push(`/${locale}/login`);
    }
  };

  const fetchUsers = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/admin/users?search=${searchTerm}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUsers(data.users);
      }
    } catch (error) {
      console.error('获取用户列表失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchNotifications = async () => {
    try {
      const response = await fetch('/api/notifications?limit=50');
      if (response.ok) {
        const data = await response.json();
        setNotifications(data);
      }
    } catch (error) {
      console.error('获取通知列表失败:', error);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      router.push(`/${locale}`);
    }
  };

  const handleUserStatusToggle = async (userId: number, currentStatus: boolean) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/admin/users/${userId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ is_active: !currentStatus })
      });

      if (response.ok) {
        fetchUsers(); // 重新获取用户列表
        alert(locale === 'zh' ? '用户状态更新成功' : 'ユーザーステータスが正常に更新されました');
      } else {
        alert(locale === 'zh' ? '更新失败' : '更新に失敗しました');
      }
    } catch (error) {
      console.error('更新用户状态失败:', error);
      alert(locale === 'zh' ? '更新失败' : '更新に失敗しました');
    }
  };

  const handleNotificationSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!notificationForm.title || !notificationForm.content) {
      alert(locale === 'zh' ? '标题和内容为必填项' : 'タイトルと内容は必須です');
      return;
    }

    setIsSubmittingNotification(true);

    try {
      const token = localStorage.getItem('token');
      const url = notificationForm.id 
        ? `/api/notifications/${notificationForm.id}`
        : '/api/notifications';
      const method = notificationForm.id ? 'PUT' : 'POST';

      const response = await fetch(url, {
        method,
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          title: notificationForm.title,
          content: notificationForm.content,
          is_active: notificationForm.is_active
        })
      });

      if (response.ok) {
        fetchNotifications();
        setShowNotificationForm(false);
        setNotificationForm({ id: null, title: '', content: '', is_active: true });
        alert(locale === 'zh' ? '操作成功' : '操作が成功しました');
      } else {
        const result = await response.json();
        alert(result.error || (locale === 'zh' ? '操作失败' : '操作に失敗しました'));
      }
    } catch (error) {
      console.error('通知操作失败:', error);
      alert(locale === 'zh' ? '操作失败' : '操作に失敗しました');
    } finally {
      setIsSubmittingNotification(false);
    }
  };

  const handleEditNotification = (notification: Notification) => {
    setNotificationForm({
      id: notification.id,
      title: notification.title,
      content: notification.content,
      is_active: notification.is_active
    });
    setShowNotificationForm(true);
  };

  const handleDeleteNotification = async (id: number) => {
    if (!confirm(locale === 'zh' ? '确定要删除这条通知吗？' : 'この通知を削除してもよろしいですか？')) {
      return;
    }

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/notifications/${id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        fetchNotifications();
        alert(locale === 'zh' ? '删除成功' : '削除が成功しました');
      } else {
        alert(locale === 'zh' ? '删除失败' : '削除に失敗しました');
      }
    } catch (error) {
      console.error('删除通知失败:', error);
      alert(locale === 'zh' ? '删除失败' : '削除に失敗しました');
    }
  };

  const getStatusDisplay = (isActive: boolean) => {
    return isActive 
      ? (locale === 'zh' ? '正常' : '正常') 
      : (locale === 'zh' ? '禁用' : '無効');
  };

  const getJobIntentionDisplay = (jobIntention: string) => {
    if (!jobIntention) return locale === 'zh' ? '未填写' : '未入力';
    return jobIntention === 'care' 
      ? (locale === 'zh' ? '介护' : '介護') 
      : (locale === 'zh' ? '护士' : '看護師');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-3 flex items-center justify-between">
          <h1 className="text-lg font-bold text-gray-800">{t('admin.title')}</h1>
          <button
            onClick={handleLogout}
            className="text-red-600 hover:text-red-700 font-medium text-sm"
          >
            {t('common.logout')}
          </button>
        </div>
      </header>

      {/* Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-4xl mx-auto px-4">
          <div className="flex">
            <button
              onClick={() => setActiveTab('users')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'users'
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('admin.userList')}
            </button>
            <button
              onClick={() => setActiveTab('notifications')}
              className={`px-6 py-3 text-sm font-medium border-b-2 transition-colors ${
                activeTab === 'notifications'
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('admin.notifications')}
            </button>
          </div>
        </div>
      </nav>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-4 py-6">
        {activeTab === 'users' && (
          <div>
            {/* Search */}
            <div className="mb-6">
              <input
                type="text"
                placeholder={locale === 'zh' ? '搜索用户（邮箱、昵称、姓名）' : 'ユーザー検索（メール、ニックネーム、名前）'}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && fetchUsers()}
                className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <button
                onClick={fetchUsers}
                className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {locale === 'zh' ? '搜索' : '検索'}
              </button>
            </div>

            {/* Users List */}
            <div className="bg-white rounded-lg shadow-sm border overflow-hidden">
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50">
                    <tr>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {locale === 'zh' ? '用户信息' : 'ユーザー情報'}
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {locale === 'zh' ? '就业意向' : '就職希望'}
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {locale === 'zh' ? '状态' : 'ステータス'}
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {locale === 'zh' ? '注册时间' : '登録日時'}
                      </th>
                      <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {locale === 'zh' ? '操作' : '操作'}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white divide-y divide-gray-200">
                    {users.map((user) => (
                      <tr key={user.id} className="hover:bg-gray-50">
                        <td className="px-4 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-900">
                              {user.nickname || user.real_name || locale === 'zh' ? '未填写' : '未入力'}
                            </div>
                            <div className="text-sm text-gray-500">{user.email}</div>
                          </div>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-900">
                          {getJobIntentionDisplay(user.job_intention || '')}
                        </td>
                        <td className="px-4 py-4">
                          <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                            user.is_active 
                              ? 'bg-green-100 text-green-800' 
                              : 'bg-red-100 text-red-800'
                          }`}>
                            {getStatusDisplay(user.is_active)}
                          </span>
                        </td>
                        <td className="px-4 py-4 text-sm text-gray-500">
                          {new Date(user.created_at).toLocaleDateString()}
                        </td>
                        <td className="px-4 py-4 text-sm">
                          <div className="flex space-x-2">
                            <button
                              onClick={() => router.push(`/${locale}/admin/users/${user.id}`)}
                              className="text-blue-600 hover:text-blue-700 font-medium"
                            >
                              {locale === 'zh' ? '详情' : '詳細'}
                            </button>
                            <button
                              onClick={() => handleUserStatusToggle(user.id, user.is_active)}
                              className={`font-medium ${
                                user.is_active 
                                  ? 'text-red-600 hover:text-red-700' 
                                  : 'text-green-600 hover:text-green-700'
                              }`}
                            >
                              {user.is_active 
                                ? (locale === 'zh' ? '禁用' : '無効化') 
                                : (locale === 'zh' ? '启用' : '有効化')
                              }
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        )}

        {activeTab === 'notifications' && (
          <div>
            {/* Add Notification Button */}
            <div className="mb-6">
              <button
                onClick={() => {
                  setNotificationForm({ id: null, title: '', content: '', is_active: true });
                  setShowNotificationForm(true);
                }}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
              >
                {t('admin.addNotification')}
              </button>
            </div>

            {/* Notification Form Modal */}
            {showNotificationForm && (
              <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
                <div className="bg-white rounded-lg p-6 w-full max-w-md mx-4">
                  <h3 className="text-lg font-semibold mb-4">
                    {notificationForm.id ? t('admin.editNotification') : t('admin.addNotification')}
                  </h3>
                  <form onSubmit={handleNotificationSubmit} className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('admin.notificationTitle')}<span className="text-red-500">*</span>
                      </label>
                      <input
                        type="text"
                        value={notificationForm.title}
                        onChange={(e) => setNotificationForm(prev => ({ ...prev, title: e.target.value }))}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        disabled={isSubmittingNotification}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">
                        {t('admin.notificationContent')}<span className="text-red-500">*</span>
                      </label>
                      <textarea
                        value={notificationForm.content}
                        onChange={(e) => setNotificationForm(prev => ({ ...prev, content: e.target.value }))}
                        rows={4}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        disabled={isSubmittingNotification}
                      />
                    </div>
                    <div className="flex items-center">
                      <input
                        type="checkbox"
                        id="is_active"
                        checked={notificationForm.is_active}
                        onChange={(e) => setNotificationForm(prev => ({ ...prev, is_active: e.target.checked }))}
                        className="mr-2"
                        disabled={isSubmittingNotification}
                      />
                      <label htmlFor="is_active" className="text-sm text-gray-700">
                        {locale === 'zh' ? '启用通知' : '通知を有効にする'}
                      </label>
                    </div>
                    <div className="flex space-x-3">
                      <button
                        type="button"
                        onClick={() => setShowNotificationForm(false)}
                        className="flex-1 px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50 transition-colors"
                        disabled={isSubmittingNotification}
                      >
                        {t('common.cancel')}
                      </button>
                      <button
                        type="submit"
                        disabled={isSubmittingNotification}
                        className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50"
                      >
                        {isSubmittingNotification 
                          ? (locale === 'zh' ? '保存中...' : '保存中...') 
                          : t('common.save')
                        }
                      </button>
                    </div>
                  </form>
                </div>
              </div>
            )}

            {/* Notifications List */}
            <div className="space-y-4">
              {notifications.map((notification) => (
                <div key={notification.id} className="bg-white rounded-lg shadow-sm border p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-semibold text-gray-800">{notification.title}</h3>
                    <div className="flex items-center space-x-2">
                      <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                        notification.is_active 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {notification.is_active 
                          ? (locale === 'zh' ? '启用' : '有効') 
                          : (locale === 'zh' ? '禁用' : '無効')
                        }
                      </span>
                      <button
                        onClick={() => handleEditNotification(notification)}
                        className="text-blue-600 hover:text-blue-700 text-sm font-medium"
                      >
                        {t('common.edit')}
                      </button>
                      <button
                        onClick={() => handleDeleteNotification(notification.id)}
                        className="text-red-600 hover:text-red-700 text-sm font-medium"
                      >
                        {t('common.delete')}
                      </button>
                    </div>
                  </div>
                  <p className="text-gray-600 mb-2">{notification.content}</p>
                  <div className="text-xs text-gray-500">
                    {locale === 'zh' ? '创建时间' : '作成日時'}: {new Date(notification.created_at).toLocaleString()}
                    {notification.created_by_name && (
                      <span className="ml-4">
                        {locale === 'zh' ? '创建者' : '作成者'}: {notification.created_by_name}
                      </span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
