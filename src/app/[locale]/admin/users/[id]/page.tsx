'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';

interface User {
  id: number;
  email: string;
  nickname?: string;
  real_name?: string;
  gender?: string;
  birthday?: string;
  hometown?: string;
  education?: string;
  job_intention?: string;
  work_experience?: string;
  japanese_level?: string;
  phone?: string;
  wechat?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface Consultation {
  id: number;
  status: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export default function UserDetailPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter();
  const t = useTranslations();
  const locale = useLocale();
  
  const [user, setUser] = useState<User | null>(null);
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [loading, setLoading] = useState(true);
  const [notes, setNotes] = useState('');
  const [isSavingNotes, setIsSavingNotes] = useState(false);

  useEffect(() => {
    checkAdminAuth();
    fetchUserDetails();
  }, []);

  const checkAdminAuth = () => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (!token || !userData) {
      router.push(`/${locale}/login`);
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      if (parsedUser.role !== 'admin') {
        router.push(`/${locale}/profile`);
        return;
      }
    } catch (error) {
      console.error('解析用户数据失败:', error);
      router.push(`/${locale}/login`);
    }
  };

  const fetchUserDetails = async () => {
    try {
      const { id } = await params;
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/admin/users/${id}`, {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const data = await response.json();
        setUser(data.user);
        setConsultations(data.consultations || []);
        // 设置最新咨询记录的备注
        if (data.consultations && data.consultations.length > 0) {
          setNotes(data.consultations[0].notes || '');
        }
      } else {
        alert(locale === 'zh' ? '获取用户详情失败' : 'ユーザー詳細の取得に失敗しました');
        router.back();
      }
    } catch (error) {
      console.error('获取用户详情失败:', error);
      alert(locale === 'zh' ? '获取用户详情失败' : 'ユーザー詳細の取得に失敗しました');
      router.back();
    } finally {
      setLoading(false);
    }
  };

  const handleStatusToggle = async () => {
    if (!user) return;

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ is_active: !user.is_active })
      });

      if (response.ok) {
        setUser(prev => prev ? { ...prev, is_active: !prev.is_active } : null);
        alert(locale === 'zh' ? '用户状态更新成功' : 'ユーザーステータスが正常に更新されました');
      } else {
        alert(locale === 'zh' ? '更新失败' : '更新に失敗しました');
      }
    } catch (error) {
      console.error('更新用户状态失败:', error);
      alert(locale === 'zh' ? '更新失败' : '更新に失敗しました');
    }
  };

  const handleSaveNotes = async () => {
    if (!user) return;

    setIsSavingNotes(true);

    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/admin/users/${user.id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({ notes })
      });

      if (response.ok) {
        alert(locale === 'zh' ? '备注保存成功' : '備考が正常に保存されました');
        fetchUserDetails(); // 重新获取数据
      } else {
        alert(locale === 'zh' ? '保存失败' : '保存に失敗しました');
      }
    } catch (error) {
      console.error('保存备注失败:', error);
      alert(locale === 'zh' ? '保存失败' : '保存に失敗しました');
    } finally {
      setIsSavingNotes(false);
    }
  };

  const getDisplayValue = (key: string, value: string) => {
    if (!value) return locale === 'zh' ? '未填写' : '未入力';
    
    switch (key) {
      case 'gender':
        return value === 'male' 
          ? (locale === 'zh' ? '男' : '男性') 
          : (locale === 'zh' ? '女' : '女性');
      case 'education':
        const educationMap: Record<string, Record<string, string>> = {
          'below_secondary': { zh: '中专及以下', ja: '中専以下' },
          'college': { zh: '大专', ja: '短大・専門学校' },
          'bachelor': { zh: '本科', ja: '大学' },
          'graduate': { zh: '研究生及以上', ja: '大学院以上' }
        };
        return educationMap[value]?.[locale] || value;
      case 'job_intention':
        return value === 'care' 
          ? (locale === 'zh' ? '介护' : '介護') 
          : (locale === 'zh' ? '护士' : '看護師');
      case 'work_experience':
        const experienceMap: Record<string, Record<string, string>> = {
          'none': { zh: '无', ja: 'なし' },
          '0-1': { zh: '0-1年', ja: '0-1年' },
          '1-2': { zh: '1-2年', ja: '1-2年' },
          '2-3': { zh: '2-3年', ja: '2-3年' },
          '3+': { zh: '3年以上', ja: '3年以上' }
        };
        return experienceMap[value]?.[locale] || value;
      case 'japanese_level':
        return value === 'none' ? (locale === 'zh' ? '无' : 'なし') : value;
      default:
        return value;
    }
  };

  const getStatusDisplay = (status: string) => {
    const statusMap: Record<string, Record<string, string>> = {
      'pending': { zh: '待处理', ja: '処理待ち' },
      'processing': { zh: '处理中', ja: '処理中' },
      'completed': { zh: '已完成', ja: '完了' },
      'cancelled': { zh: '已取消', ja: 'キャンセル' }
    };
    return statusMap[status]?.[locale] || status;
  };

  const getFieldLabel = (key: string) => {
    const labels: Record<string, Record<string, string>> = {
      email: { zh: '邮箱', ja: 'メールアドレス' },
      nickname: { zh: '昵称', ja: 'ニックネーム' },
      real_name: { zh: '真实姓名', ja: '本名' },
      gender: { zh: '性别', ja: '性別' },
      birthday: { zh: '生日', ja: '生年月日' },
      hometown: { zh: '籍贯', ja: '出身地' },
      education: { zh: '最终学历', ja: '最終学歴' },
      job_intention: { zh: '就业意向', ja: '就職希望' },
      work_experience: { zh: '工作经验', ja: '職歴' },
      japanese_level: { zh: '日语等级', ja: '日本語レベル' },
      phone: { zh: '联系电话', ja: '電話番号' },
      wechat: { zh: '微信号', ja: 'WeChat ID' }
    };
    return labels[key]?.[locale] || key;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <p className="text-gray-600">{locale === 'zh' ? '用户不存在' : 'ユーザーが存在しません'}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-4xl mx-auto px-4 py-3 flex items-center justify-between">
          <button 
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            ← {t('common.back')}
          </button>
          <h1 className="text-lg font-bold text-gray-800">{t('admin.userDetails')}</h1>
          <div className="w-12"></div>
        </div>
      </header>

      {/* Content */}
      <main className="max-w-4xl mx-auto px-4 py-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* User Info */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <div className="flex justify-between items-start mb-4">
              <h2 className="text-lg font-semibold text-gray-800">
                {locale === 'zh' ? '用户信息' : 'ユーザー情報'}
              </h2>
              <div className="flex items-center space-x-2">
                <span className={`px-2 py-1 text-xs font-semibold rounded-full ${
                  user.is_active 
                    ? 'bg-green-100 text-green-800' 
                    : 'bg-red-100 text-red-800'
                }`}>
                  {user.is_active 
                    ? (locale === 'zh' ? '正常' : '正常') 
                    : (locale === 'zh' ? '禁用' : '無効')
                  }
                </span>
                <button
                  onClick={handleStatusToggle}
                  className={`px-3 py-1 text-xs font-medium rounded ${
                    user.is_active 
                      ? 'bg-red-100 text-red-800 hover:bg-red-200' 
                      : 'bg-green-100 text-green-800 hover:bg-green-200'
                  }`}
                >
                  {user.is_active 
                    ? (locale === 'zh' ? '禁用' : '無効化') 
                    : (locale === 'zh' ? '启用' : '有効化')
                  }
                </button>
              </div>
            </div>
            
            <div className="space-y-3">
              {Object.entries(user).map(([key, value]) => {
                if (['id', 'is_active', 'created_at', 'updated_at'].includes(key)) return null;
                return (
                  <div key={key} className="flex justify-between items-start py-2 border-b border-gray-100 last:border-b-0">
                    <span className="text-sm font-medium text-gray-600 w-1/3">
                      {getFieldLabel(key)}
                    </span>
                    <span className="text-sm text-gray-800 w-2/3 text-right">
                      {getDisplayValue(key, value as string)}
                    </span>
                  </div>
                );
              })}
              <div className="flex justify-between items-start py-2">
                <span className="text-sm font-medium text-gray-600 w-1/3">
                  {locale === 'zh' ? '注册时间' : '登録日時'}
                </span>
                <span className="text-sm text-gray-800 w-2/3 text-right">
                  {new Date(user.created_at).toLocaleString()}
                </span>
              </div>
            </div>
          </div>

          {/* Notes */}
          <div className="bg-white rounded-lg shadow-sm border p-6">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? '管理员备注' : '管理者備考'}
            </h2>
            <div className="space-y-4">
              <textarea
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                rows={6}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder={locale === 'zh' ? '添加备注...' : '備考を追加...'}
                disabled={isSavingNotes}
              />
              <button
                onClick={handleSaveNotes}
                disabled={isSavingNotes}
                className="w-full bg-blue-600 text-white py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50"
              >
                {isSavingNotes 
                  ? (locale === 'zh' ? '保存中...' : '保存中...') 
                  : (locale === 'zh' ? '保存备注' : '備考を保存')
                }
              </button>
            </div>
          </div>
        </div>

        {/* Consultation History */}
        <div className="mt-6 bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">
            {locale === 'zh' ? '咨询记录' : '相談記録'}
          </h2>
          {consultations.length > 0 ? (
            <div className="space-y-4">
              {consultations.map((consultation) => (
                <div key={consultation.id} className="border border-gray-200 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-2">
                    <span className="text-sm font-medium text-gray-600">
                      {locale === 'zh' ? '状态' : 'ステータス'}:
                    </span>
                    <span className={`text-sm px-2 py-1 rounded ${
                      consultation.status === 'completed' ? 'bg-green-100 text-green-800' :
                      consultation.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                      consultation.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {getStatusDisplay(consultation.status)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mb-2">
                    {locale === 'zh' ? '提交时间' : '提出日時'}: {new Date(consultation.created_at).toLocaleString()}
                  </div>
                  {consultation.notes && (
                    <div className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                      <strong>{locale === 'zh' ? '备注' : '備考'}:</strong> {consultation.notes}
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center text-gray-500 py-8">
              {locale === 'zh' ? '暂无咨询记录' : '相談記録がありません'}
            </div>
          )}
        </div>
      </main>
    </div>
  );
}
