'use client';

import { useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState, Suspense } from 'react';
import { useTranslations, useLocale } from 'next-intl';

interface FormData {
  nickname: string;
  realName: string;
  gender: string;
  birthday: string;
  hometown: string;
  education: string;
  jobIntention: string;
  workExperience: string;
  japaneseLevel: string;
  email: string;
  password: string;
  phone: string;
  wechat: string;
}

function ConfirmPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations();
  const locale = useLocale();
  const [formData, setFormData] = useState<FormData | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  useEffect(() => {
    const dataParam = searchParams.get('data');
    if (dataParam) {
      try {
        const decodedData = JSON.parse(decodeURIComponent(dataParam));
        setFormData(decodedData);
      } catch (error) {
        console.error('解析表单数据失败:', error);
        router.push(`/${locale}/consultation`);
      }
    } else {
      router.push(`/${locale}/consultation`);
    }
  }, [searchParams, router, locale]);

  const handleConfirmSubmit = async () => {
    if (!formData || isSubmitting) return;

    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          email: formData.email,
          password: formData.password,
          nickname: formData.nickname,
          realName: formData.realName,
          gender: formData.gender,
          birthday: formData.birthday,
          hometown: formData.hometown,
          education: formData.education,
          jobIntention: formData.jobIntention,
          workExperience: formData.workExperience,
          japaneseLevel: formData.japaneseLevel,
          phone: formData.phone,
          wechat: formData.wechat
        }),
      });

      const result = await response.json();

      if (response.ok) {
        alert(t('consultation.submitSuccess'));
        router.push(`/${locale}`);
      } else {
        alert(result.error || (locale === 'zh' ? '提交失败，请稍后重试' : '送信に失敗しました。後でもう一度お試しください'));
      }
    } catch (error) {
      console.error('提交失败:', error);
      alert(locale === 'zh' ? '提交失败，请稍后重试' : '送信に失敗しました。後でもう一度お試しください');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getDisplayValue = (key: string, value: string) => {
    if (!value) return locale === 'zh' ? '未填写' : '未入力';
    
    switch (key) {
      case 'gender':
        return value === 'male' 
          ? (locale === 'zh' ? '男' : '男性') 
          : (locale === 'zh' ? '女' : '女性');
      case 'education':
        const educationMap: Record<string, Record<string, string>> = {
          'below_secondary': { zh: '中专及以下', ja: '中専以下' },
          'college': { zh: '大专', ja: '短大・専門学校' },
          'bachelor': { zh: '本科', ja: '大学' },
          'graduate': { zh: '研究生及以上', ja: '大学院以上' }
        };
        return educationMap[value]?.[locale] || value;
      case 'jobIntention':
        return value === 'care' 
          ? (locale === 'zh' ? '介护' : '介護') 
          : (locale === 'zh' ? '护士' : '看護師');
      case 'workExperience':
        const experienceMap: Record<string, Record<string, string>> = {
          'none': { zh: '无', ja: 'なし' },
          '0-1': { zh: '0-1年', ja: '0-1年' },
          '1-2': { zh: '1-2年', ja: '1-2年' },
          '2-3': { zh: '2-3年', ja: '2-3年' },
          '3+': { zh: '3年以上', ja: '3年以上' }
        };
        return experienceMap[value]?.[locale] || value;
      case 'japaneseLevel':
        return value === 'none' ? (locale === 'zh' ? '无' : 'なし') : value;
      case 'password':
        return '••••••••';
      default:
        return value;
    }
  };

  const getFieldLabel = (key: string, jobIntention: string) => {
    const labels: Record<string, Record<string, string>> = {
      nickname: { zh: '昵称', ja: 'ニックネーム' },
      realName: { zh: '真实姓名', ja: '本名' },
      gender: { zh: '性别', ja: '性別' },
      birthday: { zh: '生日', ja: '生年月日' },
      hometown: { zh: '籍贯', ja: '出身地' },
      education: { zh: '最终学历', ja: '最終学歴' },
      jobIntention: { zh: '就业意向', ja: '就職希望' },
      workExperience: { 
        zh: `${jobIntention === 'care' ? '介护' : '护士'}工作经验`, 
        ja: `${jobIntention === 'care' ? '介護' : '看護師'}職歴` 
      },
      japaneseLevel: { zh: '日语等级', ja: '日本語レベル' },
      email: { zh: '邮箱', ja: 'メールアドレス' },
      password: { zh: '密码', ja: 'パスワード' },
      phone: { zh: '联系电话', ja: '電話番号' },
      wechat: { zh: '微信号', ja: 'WeChat ID' }
    };
    return labels[key]?.[locale] || key;
  };

  if (!formData) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
          <button 
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-700 font-medium"
            disabled={isSubmitting}
          >
            ← {t('consultation.backToEdit')}
          </button>
          <h1 className="text-lg font-bold text-gray-800">{t('consultation.confirmInfo')}</h1>
          <div className="w-16"></div> {/* 占位符保持居中 */}
        </div>
      </header>

      {/* Content */}
      <main className="max-w-md mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow-sm border p-4 mb-6">
          <h2 className="text-lg font-semibold text-gray-800 mb-4">
            {locale === 'zh' ? '请确认您的信息' : '情報をご確認ください'}
          </h2>
          
          <div className="space-y-3">
            {Object.entries(formData).map(([key, value]) => (
              <div key={key} className="flex justify-between items-start py-2 border-b border-gray-100 last:border-b-0">
                <span className="text-sm font-medium text-gray-600 w-1/3">
                  {getFieldLabel(key, formData.jobIntention)}
                </span>
                <span className="text-sm text-gray-800 w-2/3 text-right">
                  {getDisplayValue(key, value)}
                </span>
              </div>
            ))}
          </div>
        </div>

        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
          <div className="flex items-start space-x-2">
            <span className="text-yellow-600 text-lg">⚠️</span>
            <div>
              <h3 className="font-semibold text-yellow-800 mb-1">{t('consultation.submitNotice')}</h3>
              <p className="text-yellow-700 text-sm">
                {t('consultation.submitNoticeText')}
              </p>
            </div>
          </div>
        </div>

        <div className="flex space-x-3">
          <button
            onClick={() => {
              // 返回时保留表单数据
              const encodedData = encodeURIComponent(JSON.stringify(formData));
              router.push(`/${locale}/consultation?data=${encodedData}`);
            }}
            className="flex-1 px-4 py-3 border border-gray-300 rounded-lg text-gray-700 font-medium hover:bg-gray-50 transition-colors"
            disabled={isSubmitting}
          >
            {t('consultation.backToEdit')}
          </button>
          <button
            onClick={handleConfirmSubmit}
            disabled={isSubmitting}
            className="flex-1 px-4 py-3 bg-blue-600 text-white rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting 
              ? (locale === 'zh' ? '提交中...' : '送信中...') 
              : t('consultation.confirmSubmit')
            }
          </button>
        </div>
      </main>
    </div>
  );
}

export default function ConfirmPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    }>
      <ConfirmPageContent />
    </Suspense>
  );
}
