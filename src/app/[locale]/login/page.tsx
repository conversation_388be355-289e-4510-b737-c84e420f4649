'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';

export default function LoginPage() {
  const router = useRouter();
  const t = useTranslations();
  const locale = useLocale();
  
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 清除该字段的错误信息
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.email.trim()) {
      newErrors.email = locale === 'zh' ? '邮箱为必填项' : 'メールアドレスは必須です';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = locale === 'zh' ? '请输入有效的邮箱地址' : '有効なメールアドレスを入力してください';
    }

    if (!formData.password.trim()) {
      newErrors.password = locale === 'zh' ? '密码为必填项' : 'パスワードは必須です';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm() || isSubmitting) return;

    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        // 登录成功，保存用户信息到localStorage
        localStorage.setItem('user', JSON.stringify(result.user));
        localStorage.setItem('token', result.token);
        
        alert(t('auth.loginSuccess'));
        
        // 根据用户角色跳转
        if (result.user.role === 'admin') {
          router.push(`/${locale}/admin`);
        } else {
          router.push(`/${locale}/profile`);
        }
      } else {
        setErrors({ general: result.error || t('auth.loginFailed') });
      }
    } catch (error) {
      console.error('登录失败:', error);
      setErrors({ general: t('auth.loginFailed') });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
          <button 
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            ← {t('common.back')}
          </button>
          <h1 className="text-lg font-bold text-gray-800">{t('auth.loginTitle')}</h1>
          <div className="w-12"></div>
        </div>
      </header>

      {/* Login Form */}
      <main className="max-w-md mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <form onSubmit={handleSubmit} className="space-y-4">
            {/* 通用错误信息 */}
            {errors.general && (
              <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                <p className="text-red-700 text-sm">{errors.general}</p>
              </div>
            )}

            {/* 邮箱 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('consultation.email')}<span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.email ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder={t('auth.emailPlaceholder')}
                disabled={isSubmitting}
              />
              {errors.email && (
                <p className="text-red-500 text-xs mt-1">{errors.email}</p>
              )}
            </div>

            {/* 密码 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                {t('consultation.password')}<span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                name="password"
                value={formData.password}
                onChange={handleInputChange}
                className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                  errors.password ? 'border-red-500' : 'border-gray-300'
                }`}
                placeholder={t('auth.passwordPlaceholder')}
                disabled={isSubmitting}
              />
              {errors.password && (
                <p className="text-red-500 text-xs mt-1">{errors.password}</p>
              )}
            </div>

            {/* 登录按钮 */}
            <button
              type="submit"
              disabled={isSubmitting}
              className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSubmitting 
                ? (locale === 'zh' ? '登录中...' : 'ログイン中...') 
                : t('auth.loginButton')
              }
            </button>
          </form>

          {/* 注册提示 */}
          <div className="mt-6 text-center">
            <p className="text-gray-600 text-sm">
              {locale === 'zh' ? '还没有账号？' : 'アカウントをお持ちでない方は？'}
              <button
                onClick={() => router.push(`/${locale}/consultation`)}
                className="text-blue-600 hover:text-blue-700 font-medium ml-1"
              >
                {locale === 'zh' ? '立即注册' : '今すぐ登録'}
              </button>
            </p>
          </div>
        </div>
      </main>
    </div>
  );
}
