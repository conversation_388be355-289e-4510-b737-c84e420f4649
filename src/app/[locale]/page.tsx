'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';
import LanguageSwitcher from '@/components/LanguageSwitcher';

function HomeContent() {
  const [activeTab, setActiveTab] = useState('home');
  const [notifications, setNotifications] = useState([]);
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const searchParams = useSearchParams();
  const t = useTranslations();
  const locale = useLocale();

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['home', 'care', 'nurse', 'about'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  useEffect(() => {
    // Check user login status
    checkUserStatus();
    // Fetch notifications
    fetchNotifications();
  }, []);

  const checkUserStatus = () => {
    const userData = localStorage.getItem('user');
    if (userData) {
      try {
        const parsedUser = JSON.parse(userData);
        setUser(parsedUser);
      } catch (error) {
        console.error('解析用户数据失败:', error);
        localStorage.removeItem('user');
        localStorage.removeItem('token');
      }
    }
  };

  const fetchNotifications = async () => {
    try {
      const response = await fetch('/api/notifications');
      if (response.ok) {
        const data = await response.json();
        setNotifications(data.slice(0, 10)); // Show max 10 notifications
      }
    } catch (error) {
      console.error('Failed to fetch notifications:', error);
    }
  };

  const handleLoginClick = () => {
    if (user) {
      // 已登录，跳转到对应页面
      if (user.role === 'admin') {
        router.push(`/${locale}/admin`);
      } else {
        router.push(`/${locale}/profile`);
      }
    } else {
      // 未登录，跳转到登录页面
      router.push(`/${locale}/login`);
    }
  };

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    const url = new URL(window.location.href);
    url.searchParams.set('tab', tabId);
    window.history.replaceState({}, '', url.toString());
  };

  const tabs = [
    { id: 'home', label: t('navigation.home') },
    { id: 'care', label: t('navigation.care') },
    { id: 'nurse', label: t('navigation.nurse') },
    { id: 'about', label: t('navigation.about') }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">{t('home.title')}</h2>
              <p className="text-gray-600 leading-relaxed">
                {t('home.description')}
              </p>
            </div>

            <div className="grid gap-4">
              <div
                className="bg-blue-50 p-4 rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors"
                onClick={() => handleTabChange('care')}
              >
                <h3 className="font-semibold text-blue-800 mb-2">{t('home.careTitle')}</h3>
                <p className="text-blue-700 text-sm">{t('home.careDescription')}</p>
              </div>

              <div
                className="bg-green-50 p-4 rounded-lg border border-green-200 cursor-pointer hover:bg-green-100 transition-colors"
                onClick={() => handleTabChange('nurse')}
              >
                <h3 className="font-semibold text-green-800 mb-2">{t('home.nurseTitle')}</h3>
                <p className="text-green-700 text-sm">{t('home.nurseDescription')}</p>
              </div>
            </div>

            {/* Notifications */}
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <div className="flex items-start space-x-2">
                <span className="text-yellow-600 text-lg">📢</span>
                <div className="flex-1">
                  <h3 className="font-semibold text-yellow-800 mb-1">{t('home.notificationTitle')}</h3>
                  {notifications.length > 0 ? (
                    <div className="space-y-2">
                      {notifications.map((notification: any) => (
                        <div key={notification.id} className="text-yellow-700 text-sm">
                          <span className="text-xs text-yellow-600">
                            {new Date(notification.created_at).toLocaleDateString()}
                          </span>
                          <p>{notification.content}</p>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <p className="text-yellow-700 text-sm">{t('home.defaultNotification')}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      case 'care':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">{t('care.title')}</h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('care.prospect')}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {locale === 'zh' 
                    ? '日本正面临严重的老龄化问题，65岁以上人口占比超过28%。预计到2025年，日本将缺少约34万名介护人员。政府大力推进外国人介护人才引进政策，为海外专业人才提供了广阔的发展机遇。'
                    : '日本は深刻な高齢化問題に直面しており、65歳以上の人口が28%を超えています。2025年までに、日本では約34万人の介護職員が不足すると予想されています。政府は外国人介護人材の導入政策を積極的に推進し、海外の専門人材に広範な発展機会を提供しています。'
                  }
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('care.workContent')}</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  {locale === 'zh' ? (
                    <>
                      <li>• 协助老年人日常生活护理</li>
                      <li>• 提供身体护理和精神支持</li>
                      <li>• 协助医疗护理工作</li>
                    </>
                  ) : (
                    <>
                      <li>• 高齢者の日常生活介護の支援</li>
                      <li>• 身体介護と精神的サポートの提供</li>
                      <li>• 医療介護業務の支援</li>
                    </>
                  )}
                </ul>
              </div>

              <div className="flex space-x-3">
                <button
                  className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  onClick={() => alert(locale === 'zh' ? '常见问答功能开发中，敬请期待！' : 'よくある質問機能は開発中です。お楽しみに！')}
                >
                  {t('care.faq')}
                </button>
                <button
                  className="flex-1 bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
                  onClick={() => router.push(`/${locale}/consultation`)}
                >
                  {t('care.consult')}
                </button>
              </div>
            </div>
          </div>
        );

      case 'nurse':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">{t('nurse.title')}</h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('nurse.prospect')}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {locale === 'zh' 
                    ? '日本医疗行业面临护士人才严重短缺，预计到2025年将缺少约27万名护士。日本政府积极推进外国护士引进政策，为具备专业资质的海外护士提供了优质的就业机会和发展平台。'
                    : '日本の医療業界は看護師人材の深刻な不足に直面しており、2025年までに約27万人の看護師が不足すると予想されています。日本政府は外国人看護師の導入政策を積極的に推進し、専門資格を持つ海外の看護師に質の高い就職機会と発展プラットフォームを提供しています。'
                  }
                </p>
              </div>

              <div className="flex space-x-3">
                <button
                  className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  onClick={() => alert(locale === 'zh' ? '常见问答功能开发中，敬请期待！' : 'よくある質問機能は開発中です。お楽しみに！')}
                >
                  {t('nurse.faq')}
                </button>
                <button
                  className="flex-1 bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
                  onClick={() => router.push(`/${locale}/consultation`)}
                >
                  {t('nurse.consult')}
                </button>
              </div>
            </div>
          </div>
        );

      case 'about':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">{t('about.title')}</h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">{t('about.companyIntro')}</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  {locale === 'zh' 
                    ? '我们是一家专业的国际人才交流机构，专注于为中国专业人才提供赴日工作的机会和服务。凭借多年的行业经验和丰富的资源，我们已成功帮助数千名专业人才实现了赴日工作的梦想。'
                    : '私たちは専門的な国際人材交流機関であり、中国の専門人材に日本での就職機会とサービスを提供することに専念しています。長年の業界経験と豊富なリソースにより、数千人の専門人材が日本での就職の夢を実現するお手伝いをしてきました。'
                  }
                </p>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">日</span>
            </div>
            <h1 className="text-lg font-bold text-gray-800">
              {locale === 'zh' ? '赴日人才交流' : '日本人材交流'}
            </h1>
          </div>
          <div className="flex items-center space-x-2">
            <LanguageSwitcher />
            <button
              onClick={handleLoginClick}
              className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
            >
              {user ? (user.nickname || user.email) : t('common.login')}
            </button>
          </div>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-md mx-auto px-4">
          <div className="flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`flex-1 py-3 text-sm font-medium text-center border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-md mx-auto px-4 py-6">
        {renderContent()}
      </main>
    </div>
  );
}

export default function Home() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    }>
      <HomeContent />
    </Suspense>
  );
}
