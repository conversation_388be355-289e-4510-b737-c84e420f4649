'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useTranslations, useLocale } from 'next-intl';

interface User {
  id: number;
  email: string;
  nickname?: string;
  real_name?: string;
  gender?: string;
  birthday?: string;
  hometown?: string;
  education?: string;
  job_intention?: string;
  work_experience?: string;
  japanese_level?: string;
  phone?: string;
  wechat?: string;
  role: string;
  created_at: string;
}

interface Consultation {
  id: number;
  status: string;
  notes?: string;
  created_at: string;
  updated_at: string;
}

export default function ProfilePage() {
  const router = useRouter();
  const t = useTranslations();
  const locale = useLocale();
  
  const [user, setUser] = useState<User | null>(null);
  const [consultations, setConsultations] = useState<Consultation[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState('info');
  const [changePasswordForm, setChangePasswordForm] = useState({
    oldPassword: '',
    newPassword: '',
    confirmPassword: ''
  });
  const [passwordErrors, setPasswordErrors] = useState<Record<string, string>>({});
  const [isChangingPassword, setIsChangingPassword] = useState(false);

  useEffect(() => {
    checkAuth();
    fetchUserData();
  }, []);

  const checkAuth = () => {
    const token = localStorage.getItem('token');
    const userData = localStorage.getItem('user');
    
    if (!token || !userData) {
      router.push(`/${locale}/login`);
      return;
    }

    try {
      const parsedUser = JSON.parse(userData);
      if (parsedUser.role === 'admin') {
        router.push(`/${locale}/admin`);
        return;
      }
    } catch (error) {
      console.error('解析用户数据失败:', error);
      router.push(`/${locale}/login`);
    }
  };

  const fetchUserData = async () => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/auth/me', {
        headers: {
          'Authorization': `Bearer ${token}`
        }
      });

      if (response.ok) {
        const userData = await response.json();
        setUser(userData);
        
        // 获取咨询记录
        const consultResponse = await fetch(`/api/admin/users/${userData.id}`, {
          headers: {
            'Authorization': `Bearer ${token}`
          }
        });
        
        if (consultResponse.ok) {
          const consultData = await consultResponse.json();
          setConsultations(consultData.consultations || []);
        }
      } else {
        router.push(`/${locale}/login`);
      }
    } catch (error) {
      console.error('获取用户数据失败:', error);
      router.push(`/${locale}/login`);
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = async () => {
    try {
      await fetch('/api/auth/logout', { method: 'POST' });
    } catch (error) {
      console.error('退出登录失败:', error);
    } finally {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      router.push(`/${locale}`);
    }
  };

  const handlePasswordChange = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const newErrors: Record<string, string> = {};
    
    if (!changePasswordForm.oldPassword) {
      newErrors.oldPassword = t('auth.oldPassword') + t('common.required');
    }
    
    if (!changePasswordForm.newPassword) {
      newErrors.newPassword = t('auth.newPassword') + t('common.required');
    } else if (changePasswordForm.newPassword.length < 6) {
      newErrors.newPassword = locale === 'zh' ? '新密码至少需要6位字符' : '新しいパスワードは少なくとも6文字必要です';
    }
    
    if (changePasswordForm.newPassword !== changePasswordForm.confirmPassword) {
      newErrors.confirmPassword = locale === 'zh' ? '确认密码不匹配' : 'パスワードが一致しません';
    }

    setPasswordErrors(newErrors);
    
    if (Object.keys(newErrors).length > 0) return;

    setIsChangingPassword(true);
    
    try {
      const token = localStorage.getItem('token');
      const response = await fetch('/api/auth/change-password', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          oldPassword: changePasswordForm.oldPassword,
          newPassword: changePasswordForm.newPassword
        })
      });

      const result = await response.json();

      if (response.ok) {
        alert(locale === 'zh' ? '密码修改成功' : 'パスワードが正常に変更されました');
        setChangePasswordForm({ oldPassword: '', newPassword: '', confirmPassword: '' });
        setActiveTab('info');
      } else {
        setPasswordErrors({ general: result.error });
      }
    } catch (error) {
      console.error('修改密码失败:', error);
      setPasswordErrors({ general: locale === 'zh' ? '修改密码失败' : 'パスワードの変更に失敗しました' });
    } finally {
      setIsChangingPassword(false);
    }
  };

  const getDisplayValue = (key: string, value: string) => {
    if (!value) return locale === 'zh' ? '未填写' : '未入力';
    
    switch (key) {
      case 'gender':
        return value === 'male' 
          ? (locale === 'zh' ? '男' : '男性') 
          : (locale === 'zh' ? '女' : '女性');
      case 'education':
        const educationMap: Record<string, Record<string, string>> = {
          'below_secondary': { zh: '中专及以下', ja: '中専以下' },
          'college': { zh: '大专', ja: '短大・専門学校' },
          'bachelor': { zh: '本科', ja: '大学' },
          'graduate': { zh: '研究生及以上', ja: '大学院以上' }
        };
        return educationMap[value]?.[locale] || value;
      case 'job_intention':
        return value === 'care' 
          ? (locale === 'zh' ? '介护' : '介護') 
          : (locale === 'zh' ? '护士' : '看護師');
      case 'work_experience':
        const experienceMap: Record<string, Record<string, string>> = {
          'none': { zh: '无', ja: 'なし' },
          '0-1': { zh: '0-1年', ja: '0-1年' },
          '1-2': { zh: '1-2年', ja: '1-2年' },
          '2-3': { zh: '2-3年', ja: '2-3年' },
          '3+': { zh: '3年以上', ja: '3年以上' }
        };
        return experienceMap[value]?.[locale] || value;
      case 'japanese_level':
        return value === 'none' ? (locale === 'zh' ? '无' : 'なし') : value;
      default:
        return value;
    }
  };

  const getStatusDisplay = (status: string) => {
    const statusMap: Record<string, Record<string, string>> = {
      'pending': { zh: '待处理', ja: '処理待ち' },
      'processing': { zh: '处理中', ja: '処理中' },
      'completed': { zh: '已完成', ja: '完了' },
      'cancelled': { zh: '已取消', ja: 'キャンセル' }
    };
    return statusMap[status]?.[locale] || status;
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">{t('common.loading')}</p>
        </div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
          <button 
            onClick={() => router.push(`/${locale}`)}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            ← {t('common.back')}
          </button>
          <h1 className="text-lg font-bold text-gray-800">{t('auth.profile')}</h1>
          <button
            onClick={handleLogout}
            className="text-red-600 hover:text-red-700 font-medium text-sm"
          >
            {t('common.logout')}
          </button>
        </div>
      </header>

      {/* Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-md mx-auto px-4">
          <div className="flex">
            <button
              onClick={() => setActiveTab('info')}
              className={`flex-1 py-3 text-sm font-medium text-center border-b-2 transition-colors ${
                activeTab === 'info'
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {locale === 'zh' ? '个人信息' : '個人情報'}
            </button>
            <button
              onClick={() => setActiveTab('consultations')}
              className={`flex-1 py-3 text-sm font-medium text-center border-b-2 transition-colors ${
                activeTab === 'consultations'
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {locale === 'zh' ? '咨询记录' : '相談記録'}
            </button>
            <button
              onClick={() => setActiveTab('password')}
              className={`flex-1 py-3 text-sm font-medium text-center border-b-2 transition-colors ${
                activeTab === 'password'
                  ? 'border-blue-600 text-blue-600'
                  : 'border-transparent text-gray-500 hover:text-gray-700'
              }`}
            >
              {t('auth.changePassword')}
            </button>
          </div>
        </div>
      </nav>

      {/* Content */}
      <main className="max-w-md mx-auto px-4 py-6">
        {activeTab === 'info' && (
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              {locale === 'zh' ? '个人信息' : '個人情報'}
            </h2>
            <div className="space-y-3">
              {Object.entries(user).map(([key, value]) => {
                if (['id', 'role', 'created_at', 'updated_at'].includes(key)) return null;
                return (
                  <div key={key} className="flex justify-between items-start py-2 border-b border-gray-100 last:border-b-0">
                    <span className="text-sm font-medium text-gray-600 w-1/3">
                      {key === 'email' ? t('consultation.email') :
                       key === 'nickname' ? t('consultation.nickname') :
                       key === 'real_name' ? t('consultation.realName') :
                       key === 'gender' ? t('consultation.gender') :
                       key === 'birthday' ? t('consultation.birthday') :
                       key === 'hometown' ? t('consultation.hometown') :
                       key === 'education' ? t('consultation.education') :
                       key === 'job_intention' ? t('consultation.jobIntention') :
                       key === 'work_experience' ? t('consultation.workExperience') :
                       key === 'japanese_level' ? t('consultation.japaneseLevel') :
                       key === 'phone' ? t('consultation.phone') :
                       key === 'wechat' ? t('consultation.wechat') : key}
                    </span>
                    <span className="text-sm text-gray-800 w-2/3 text-right">
                      {getDisplayValue(key, value as string)}
                    </span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {activeTab === 'consultations' && (
          <div className="space-y-4">
            <h2 className="text-lg font-semibold text-gray-800">
              {locale === 'zh' ? '咨询记录' : '相談記録'}
            </h2>
            {consultations.length > 0 ? (
              consultations.map((consultation) => (
                <div key={consultation.id} className="bg-white rounded-lg shadow-sm border p-4">
                  <div className="flex justify-between items-start mb-2">
                    <span className="text-sm font-medium text-gray-600">
                      {locale === 'zh' ? '状态' : 'ステータス'}:
                    </span>
                    <span className={`text-sm px-2 py-1 rounded ${
                      consultation.status === 'completed' ? 'bg-green-100 text-green-800' :
                      consultation.status === 'processing' ? 'bg-blue-100 text-blue-800' :
                      consultation.status === 'cancelled' ? 'bg-red-100 text-red-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {getStatusDisplay(consultation.status)}
                    </span>
                  </div>
                  <div className="text-xs text-gray-500 mb-2">
                    {locale === 'zh' ? '提交时间' : '提出日時'}: {new Date(consultation.created_at).toLocaleString()}
                  </div>
                  {consultation.notes && (
                    <div className="text-sm text-gray-700 bg-gray-50 p-2 rounded">
                      <strong>{locale === 'zh' ? '备注' : '備考'}:</strong> {consultation.notes}
                    </div>
                  )}
                </div>
              ))
            ) : (
              <div className="bg-white rounded-lg shadow-sm border p-4 text-center text-gray-500">
                {locale === 'zh' ? '暂无咨询记录' : '相談記録がありません'}
              </div>
            )}
          </div>
        )}

        {activeTab === 'password' && (
          <div className="bg-white rounded-lg shadow-sm border p-4">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">
              {t('auth.changePassword')}
            </h2>
            <form onSubmit={handlePasswordChange} className="space-y-4">
              {passwordErrors.general && (
                <div className="bg-red-50 border border-red-200 rounded-lg p-3">
                  <p className="text-red-700 text-sm">{passwordErrors.general}</p>
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('auth.oldPassword')}<span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  value={changePasswordForm.oldPassword}
                  onChange={(e) => setChangePasswordForm(prev => ({ ...prev, oldPassword: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    passwordErrors.oldPassword ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={isChangingPassword}
                />
                {passwordErrors.oldPassword && (
                  <p className="text-red-500 text-xs mt-1">{passwordErrors.oldPassword}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('auth.newPassword')}<span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  value={changePasswordForm.newPassword}
                  onChange={(e) => setChangePasswordForm(prev => ({ ...prev, newPassword: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    passwordErrors.newPassword ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={isChangingPassword}
                />
                {passwordErrors.newPassword && (
                  <p className="text-red-500 text-xs mt-1">{passwordErrors.newPassword}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {t('auth.confirmPassword')}<span className="text-red-500">*</span>
                </label>
                <input
                  type="password"
                  value={changePasswordForm.confirmPassword}
                  onChange={(e) => setChangePasswordForm(prev => ({ ...prev, confirmPassword: e.target.value }))}
                  className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                    passwordErrors.confirmPassword ? 'border-red-500' : 'border-gray-300'
                  }`}
                  disabled={isChangingPassword}
                />
                {passwordErrors.confirmPassword && (
                  <p className="text-red-500 text-xs mt-1">{passwordErrors.confirmPassword}</p>
                )}
              </div>

              <button
                type="submit"
                disabled={isChangingPassword}
                className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isChangingPassword 
                  ? (locale === 'zh' ? '修改中...' : '変更中...') 
                  : (locale === 'zh' ? '修改密码' : 'パスワード変更')
                }
              </button>
            </form>
          </div>
        )}
      </main>
    </div>
  );
}
