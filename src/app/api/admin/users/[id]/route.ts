import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest, isAdmin } from '@/lib/auth';

// 获取用户详情（仅管理员）
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = getUserFromRequest(request);

    if (!user || !isAdmin(user)) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      );
    }

    const { id } = await params;
    const userId = parseInt(id);

    // 获取用户基本信息
    const users = await query(
      `SELECT id, email, nickname, real_name, gender, birthday, hometown,
              education, job_intention, work_experience, japanese_level,
              phone, wechat, is_active, created_at, updated_at
       FROM users WHERE id = ? AND role = "user"`,
      [userId]
    ) as any[];

    if (users.length === 0) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      );
    }

    const userData = users[0];

    // 获取用户的咨询记录
    const consultations = await query(
      `SELECT id, nickname, real_name, gender, birthday, hometown,
              education, job_intention, work_experience, japanese_level,
              phone, wechat, status, notes, created_at, updated_at
       FROM consultations WHERE user_id = ?
       ORDER BY created_at DESC`,
      [userId]
    ) as any[];

    return NextResponse.json({
      user: userData,
      consultations
    }, { status: 200 });

  } catch (error) {
    console.error('Get user details error:', error);
    return NextResponse.json(
      { error: '获取用户详情失败' },
      { status: 500 }
    );
  }
}

// 更新用户状态（仅管理员）
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const user = getUserFromRequest(request);

    if (!user || !isAdmin(user)) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { is_active, notes } = body;
    const { id } = await params;
    const userId = parseInt(id);

    await query(
      'UPDATE users SET is_active = ? WHERE id = ? AND role = "user"',
      [is_active, userId]
    );

    // 如果有备注，更新最新的咨询记录
    if (notes !== undefined) {
      await query(
        `UPDATE consultations SET notes = ? 
         WHERE user_id = ? 
         ORDER BY created_at DESC 
         LIMIT 1`,
        [notes, userId]
      );
    }

    return NextResponse.json(
      { message: '用户状态更新成功' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Update user status error:', error);
    return NextResponse.json(
      { error: '更新用户状态失败' },
      { status: 500 }
    );
  }
}
