import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest, isAdmin } from '@/lib/auth';

// 获取用户列表（仅管理员）
export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    
    if (!user || !isAdmin(user)) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') || '20';
    const offset = searchParams.get('offset') || '0';
    const search = searchParams.get('search') || '';

    let whereClause = 'WHERE role = ?';
    let queryParams: any[] = ['user'];

    if (search) {
      whereClause += ' AND (email LIKE ? OR nickname LIKE ? OR real_name LIKE ?)';
      const searchPattern = `%${search}%`;
      queryParams.push(searchPattern, searchPattern, searchPattern);
    }

    // 获取用户列表
    const users = await query(
      `SELECT id, email, nickname, real_name, gender, birthday, hometown,
              education, job_intention, work_experience, japanese_level,
              phone, wechat, is_active, created_at
       FROM users
       ${whereClause}
       ORDER BY created_at DESC
       LIMIT ? OFFSET ?`,
      [...queryParams, limit, offset]
    ) as any[];

    // 获取总数
    const totalResult = await query(
      `SELECT COUNT(*) as total FROM users ${whereClause}`,
      queryParams
    ) as any[];

    const total = totalResult[0].total;

    return NextResponse.json({
      users,
      total,
      limit,
      offset
    }, { status: 200 });

  } catch (error) {
    console.error('Get users error:', error);
    return NextResponse.json(
      { error: '获取用户列表失败' },
      { status: 500 }
    );
  }
}
