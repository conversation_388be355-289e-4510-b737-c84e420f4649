import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest, verifyPassword, hashPassword } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    
    if (!user) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      );
    }

    const body = await request.json();
    const { oldPassword, newPassword } = body;

    // 验证必填字段
    if (!oldPassword || !newPassword) {
      return NextResponse.json(
        { error: '原密码和新密码为必填项' },
        { status: 400 }
      );
    }

    // 验证新密码强度
    if (newPassword.length < 6) {
      return NextResponse.json(
        { error: '新密码至少需要6位字符' },
        { status: 400 }
      );
    }

    // 获取用户当前密码
    const users = await query(
      'SELECT password FROM users WHERE id = ?',
      [user.userId]
    ) as any[];

    if (users.length === 0) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      );
    }

    const currentUser = users[0];

    // 验证原密码
    const isOldPasswordValid = await verifyPassword(oldPassword, currentUser.password);
    if (!isOldPasswordValid) {
      return NextResponse.json(
        { error: '原密码错误' },
        { status: 400 }
      );
    }

    // 哈希新密码
    const hashedNewPassword = await hashPassword(newPassword);

    // 更新密码
    await query(
      'UPDATE users SET password = ? WHERE id = ?',
      [hashedNewPassword, user.userId]
    );

    return NextResponse.json(
      { message: '密码修改成功' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Change password error:', error);
    return NextResponse.json(
      { error: '修改密码失败，请稍后重试' },
      { status: 500 }
    );
  }
}
