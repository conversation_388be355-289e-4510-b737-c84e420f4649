import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    
    if (!user) {
      return NextResponse.json(
        { error: '未登录' },
        { status: 401 }
      );
    }

    // 获取用户详细信息
    const users = await query(
      `SELECT id, email, nickname, real_name, gender, birthday, hometown,
              education, job_intention, work_experience, japanese_level, 
              phone, wechat, role, is_active, created_at
       FROM users WHERE id = ?`,
      [user.userId]
    ) as any[];

    if (users.length === 0) {
      return NextResponse.json(
        { error: '用户不存在' },
        { status: 404 }
      );
    }

    const userData = users[0];
    
    // 移除敏感信息
    delete userData.password;

    return NextResponse.json(userData, { status: 200 });

  } catch (error) {
    console.error('Get user info error:', error);
    return NextResponse.json(
      { error: '获取用户信息失败' },
      { status: 500 }
    );
  }
}
