import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { hashPassword, isValidEmail, isValidPassword } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      email,
      password,
      nickname,
      realName,
      gender,
      birthday,
      hometown,
      education,
      jobIntention,
      workExperience,
      japaneseLevel,
      phone,
      wechat
    } = body;

    // 验证必填字段
    if (!email || !password || !nickname) {
      return NextResponse.json(
        { error: '邮箱、密码和昵称为必填项' },
        { status: 400 }
      );
    }

    // 验证邮箱格式
    if (!isValidEmail(email)) {
      return NextResponse.json(
        { error: '请输入有效的邮箱地址' },
        { status: 400 }
      );
    }

    // 验证密码强度
    if (!isValidPassword(password)) {
      return NextResponse.json(
        { error: '密码至少需要6位字符' },
        { status: 400 }
      );
    }

    // 检查邮箱是否已存在
    const existingUser = await query(
      'SELECT id FROM users WHERE email = ?',
      [email]
    ) as any[];

    if (existingUser.length > 0) {
      return NextResponse.json(
        { error: '该邮箱已被注册' },
        { status: 409 }
      );
    }

    // 哈希密码
    const hashedPassword = await hashPassword(password);

    // 插入新用户
    const result = await query(
      `INSERT INTO users (
        email, password, nickname, real_name, gender, birthday, hometown,
        education, job_intention, work_experience, japanese_level, phone, wechat
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
      [
        email,
        hashedPassword,
        nickname,
        realName || null,
        gender || null,
        birthday || null,
        hometown || null,
        education || null,
        jobIntention || null,
        workExperience || null,
        japaneseLevel || null,
        phone || null,
        wechat || null
      ]
    ) as any;

    // 同时创建咨询记录
    if (result.insertId) {
      await query(
        `INSERT INTO consultations (
          user_id, nickname, real_name, gender, birthday, hometown,
          education, job_intention, work_experience, japanese_level, phone, wechat
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          result.insertId,
          nickname,
          realName || null,
          gender || null,
          birthday || null,
          hometown || null,
          education || null,
          jobIntention || null,
          workExperience || null,
          japaneseLevel || null,
          phone || null,
          wechat || null
        ]
      );
    }

    return NextResponse.json(
      { 
        message: '注册成功',
        userId: result.insertId
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Registration error:', error);
    return NextResponse.json(
      { error: '注册失败，请稍后重试' },
      { status: 500 }
    );
  }
}
