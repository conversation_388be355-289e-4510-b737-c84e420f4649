import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest, isAdmin } from '@/lib/auth';

// 更新通知（仅管理员）
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = getUserFromRequest(request);
    
    if (!user || !isAdmin(user)) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { title, content, is_active } = body;
    const notificationId = parseInt(params.id);

    if (!title || !content) {
      return NextResponse.json(
        { error: '标题和内容为必填项' },
        { status: 400 }
      );
    }

    await query(
      'UPDATE notifications SET title = ?, content = ?, is_active = ? WHERE id = ?',
      [title, content, is_active !== undefined ? is_active : true, notificationId]
    );

    return NextResponse.json(
      { message: '通知更新成功' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Update notification error:', error);
    return NextResponse.json(
      { error: '更新通知失败' },
      { status: 500 }
    );
  }
}

// 删除通知（仅管理员）
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const user = getUserFromRequest(request);
    
    if (!user || !isAdmin(user)) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      );
    }

    const notificationId = parseInt(params.id);

    await query(
      'UPDATE notifications SET is_active = FALSE WHERE id = ?',
      [notificationId]
    );

    return NextResponse.json(
      { message: '通知删除成功' },
      { status: 200 }
    );

  } catch (error) {
    console.error('Delete notification error:', error);
    return NextResponse.json(
      { error: '删除通知失败' },
      { status: 500 }
    );
  }
}
