import { NextRequest, NextResponse } from 'next/server';
import { query } from '@/lib/db';
import { getUserFromRequest, isAdmin } from '@/lib/auth';

// 获取通知列表
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') || '10';
    const offset = searchParams.get('offset') || '0';

    const notifications = await query(
      `SELECT n.id, n.title, n.content, n.is_active, n.created_at, n.updated_at,
              u.nickname as created_by_name
       FROM notifications n
       LEFT JOIN users u ON n.created_by = u.id
       WHERE n.is_active = TRUE
       ORDER BY n.created_at DESC
       LIMIT ? OFFSET ?`,
      [limit, offset]
    ) as any[];

    return NextResponse.json(notifications, { status: 200 });

  } catch (error) {
    console.error('Get notifications error:', error);
    return NextResponse.json(
      { error: '获取通知失败' },
      { status: 500 }
    );
  }
}

// 创建新通知（仅管理员）
export async function POST(request: NextRequest) {
  try {
    const user = getUserFromRequest(request);
    
    if (!user || !isAdmin(user)) {
      return NextResponse.json(
        { error: '权限不足' },
        { status: 403 }
      );
    }

    const body = await request.json();
    const { title, content } = body;

    if (!title || !content) {
      return NextResponse.json(
        { error: '标题和内容为必填项' },
        { status: 400 }
      );
    }

    const result = await query(
      'INSERT INTO notifications (title, content, created_by) VALUES (?, ?, ?)',
      [title, content, user.userId]
    ) as any;

    return NextResponse.json(
      { 
        message: '通知创建成功',
        id: result.insertId
      },
      { status: 201 }
    );

  } catch (error) {
    console.error('Create notification error:', error);
    return NextResponse.json(
      { error: '创建通知失败' },
      { status: 500 }
    );
  }
}
