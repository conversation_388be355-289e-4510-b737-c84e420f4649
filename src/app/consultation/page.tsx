'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

interface FormData {
  nickname: string;
  realName: string;
  gender: string;
  birthday: string;
  hometown: string;
  education: string;
  jobIntention: string;
  workExperience: string;
  japaneseLevel: string;
  email: string;
  password: string;
  phone: string;
  wechat: string;
}

function ConsultationPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const [formData, setFormData] = useState<FormData>({
    nickname: '',
    realName: '',
    gender: 'female', // 默认选女
    birthday: '',
    hometown: '',
    education: '',
    jobIntention: '',
    workExperience: 'none', // 默认选无
    japaneseLevel: 'none', // 默认选无
    email: '',
    password: '',
    phone: '',
    wechat: ''
  });

  const [errors, setErrors] = useState<Record<string, string>>({});

  // 从URL参数恢复表单数据
  useEffect(() => {
    const dataParam = searchParams.get('data');
    if (dataParam) {
      try {
        const decodedData = JSON.parse(decodeURIComponent(dataParam));
        setFormData(decodedData);
      } catch (error) {
        console.error('解析表单数据失败:', error);
      }
    }
  }, [searchParams]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // 清除该字段的错误信息
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    // 必填字段验证
    if (!formData.nickname.trim()) newErrors.nickname = '昵称为必填项';
    if (!formData.gender) newErrors.gender = '性别为必填项';
    if (!formData.birthday) newErrors.birthday = '生日为必填项';
    if (!formData.education) newErrors.education = '最终学历为必填项';
    if (!formData.jobIntention) newErrors.jobIntention = '就业意向为必填项';
    if (!formData.japaneseLevel) newErrors.japaneseLevel = '日语等级为必填项';
    if (!formData.email.trim()) newErrors.email = '邮箱为必填项';
    if (!formData.password.trim()) newErrors.password = '密码为必填项';

    // 工作经验验证
    if (formData.jobIntention) {
      if (!formData.workExperience) newErrors.workExperience = '工作经验为必填项';
    }

    // 邮箱格式验证
    if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = '请输入有效的邮箱地址';
    }

    // 密码长度验证
    if (formData.password && formData.password.length < 6) {
      newErrors.password = '密码至少需要6位字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      // 将表单数据编码并传递到确认页面
      const encodedData = encodeURIComponent(JSON.stringify(formData));
      router.push(`/consultation/confirm?data=${encodedData}`);
    }
  };

  const renderWorkExperienceFields = () => {
    if (!formData.jobIntention) return null;

    const jobType = formData.jobIntention === 'care' ? '介护' : '护士';

    return (
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          {jobType}工作经验<span className="text-red-500">*</span>
        </label>
        <select
          name="workExperience"
          value={formData.workExperience}
          onChange={handleInputChange}
          className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
            errors.workExperience ? 'border-red-500' : 'border-gray-300'
          }`}
        >
          <option value="">请选择工作经验</option>
          <option value="none">无</option>
          <option value="0-1">0-1年</option>
          <option value="1-2">1-2年</option>
          <option value="2-3">2-3年</option>
          <option value="3+">3年以上</option>
        </select>
        {errors.workExperience && (
          <p className="text-red-500 text-xs mt-1">{errors.workExperience}</p>
        )}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
          <button 
            onClick={() => router.back()}
            className="text-blue-600 hover:text-blue-700 font-medium"
          >
            ← 返回
          </button>
          <h1 className="text-lg font-bold text-gray-800">我要咨询</h1>
          <div className="w-12"></div> {/* 占位符保持居中 */}
        </div>
      </header>

      {/* Form */}
      <main className="max-w-md mx-auto px-4 py-6">
        <form onSubmit={handleSubmit} className="space-y-4">
          {/* 昵称 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              昵称<span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              name="nickname"
              value={formData.nickname}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.nickname ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="请输入昵称"
            />
            {errors.nickname && (
              <p className="text-red-500 text-xs mt-1">{errors.nickname}</p>
            )}
          </div>

          {/* 真实姓名 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              真实姓名<span className="text-gray-400">（可以以后补全）</span>
            </label>
            <input
              type="text"
              name="realName"
              value={formData.realName}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入真实姓名"
            />
          </div>

          {/* 性别 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              性别<span className="text-red-500">*</span>
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="gender"
                  value="male"
                  checked={formData.gender === 'male'}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                男
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="gender"
                  value="female"
                  checked={formData.gender === 'female'}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                女
              </label>
            </div>
            {errors.gender && (
              <p className="text-red-500 text-xs mt-1">{errors.gender}</p>
            )}
          </div>

          {/* 生日 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              生日<span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              name="birthday"
              value={formData.birthday}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.birthday ? 'border-red-500' : 'border-gray-300'
              }`}
            />
            {errors.birthday && (
              <p className="text-red-500 text-xs mt-1">{errors.birthday}</p>
            )}
          </div>

          {/* 籍贯 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              籍贯<span className="text-gray-400">（可以以后补全）</span>
            </label>
            <input
              type="text"
              name="hometown"
              value={formData.hometown}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入籍贯"
            />
          </div>

          {/* 最终学历 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              最终学历<span className="text-red-500">*</span>
            </label>
            <select
              name="education"
              value={formData.education}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.education ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="">请选择学历</option>
              <option value="below_secondary">中专及以下</option>
              <option value="college">大专</option>
              <option value="bachelor">本科</option>
              <option value="graduate">研究生及以上</option>
            </select>
            {errors.education && (
              <p className="text-red-500 text-xs mt-1">{errors.education}</p>
            )}
          </div>

          {/* 就业意向 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              就业意向<span className="text-red-500">*</span>
            </label>
            <div className="flex space-x-4">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="jobIntention"
                  value="care"
                  checked={formData.jobIntention === 'care'}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                介护
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="jobIntention"
                  value="nurse"
                  checked={formData.jobIntention === 'nurse'}
                  onChange={handleInputChange}
                  className="mr-2"
                />
                护士
              </label>
            </div>
            {errors.jobIntention && (
              <p className="text-red-500 text-xs mt-1">{errors.jobIntention}</p>
            )}
          </div>

          {/* 工作经验 */}
          {renderWorkExperienceFields()}

          {/* 日语等级 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              日语等级<span className="text-red-500">*</span>
            </label>
            <select
              name="japaneseLevel"
              value={formData.japaneseLevel}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.japaneseLevel ? 'border-red-500' : 'border-gray-300'
              }`}
            >
              <option value="none">无</option>
              <option value="N5">N5</option>
              <option value="N4">N4</option>
              <option value="N3">N3</option>
              <option value="N2">N2</option>
              <option value="N1">N1</option>
            </select>
            {errors.japaneseLevel && (
              <p className="text-red-500 text-xs mt-1">{errors.japaneseLevel}</p>
            )}
          </div>

          {/* 邮箱 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              邮箱<span className="text-red-500">*</span>
            </label>
            <input
              type="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.email ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="请输入邮箱地址"
            />
            {errors.email && (
              <p className="text-red-500 text-xs mt-1">{errors.email}</p>
            )}
          </div>

          {/* 密码 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              密码<span className="text-red-500">*</span>
            </label>
            <input
              type="password"
              name="password"
              value={formData.password}
              onChange={handleInputChange}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 ${
                errors.password ? 'border-red-500' : 'border-gray-300'
              }`}
              placeholder="请输入密码（至少6位）"
            />
            {errors.password && (
              <p className="text-red-500 text-xs mt-1">{errors.password}</p>
            )}
          </div>

          {/* 联系电话 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              联系电话<span className="text-gray-400">（可以以后补全）</span>
            </label>
            <input
              type="tel"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入联系电话"
            />
          </div>

          {/* 微信号 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              微信号<span className="text-gray-400">（可以以后补全）</span>
            </label>
            <input
              type="text"
              name="wechat"
              value={formData.wechat}
              onChange={handleInputChange}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              placeholder="请输入微信号"
            />
          </div>

          {/* 提交按钮 */}
          <button
            type="submit"
            className="w-full bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors mt-6"
          >
            提交咨询信息
          </button>
        </form>
      </main>


    </div>
  );
}

export default function ConsultationPage() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    }>
      <ConsultationPageContent />
    </Suspense>
  );
}
