'use client';

import { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';

function HomeContent() {
  const [activeTab, setActiveTab] = useState('home');
  const router = useRouter();
  const searchParams = useSearchParams();

  useEffect(() => {
    const tab = searchParams.get('tab');
    if (tab && ['home', 'care', 'nurse', 'about'].includes(tab)) {
      setActiveTab(tab);
    }
  }, [searchParams]);

  const handleTabChange = (tabId: string) => {
    setActiveTab(tabId);
    const url = new URL(window.location.href);
    url.searchParams.set('tab', tabId);
    window.history.replaceState({}, '', url.toString());
  };

  const tabs = [
    { id: 'home', label: '首页' },
    { id: 'care', label: '赴日介护' },
    { id: 'nurse', label: '赴日护士' },
    { id: 'about', label: '关于我们' }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'home':
        return (
          <div className="space-y-6">
            <div className="text-center">
              <h2 className="text-2xl font-bold text-gray-800 mb-4">欢迎来到赴日人才交流平台</h2>
              <p className="text-gray-600 leading-relaxed">
                我们致力于为有志于赴日工作的专业人才提供优质的服务和机会。
                无论您是希望从事介护工作还是护士工作，我们都将为您提供专业的指导和支持。
              </p>
            </div>

            <div className="grid gap-4">
              <div
                className="bg-blue-50 p-4 rounded-lg border border-blue-200 cursor-pointer hover:bg-blue-100 transition-colors"
                onClick={() => handleTabChange('care')}
              >
                <h3 className="font-semibold text-blue-800 mb-2">🏥 赴日介护</h3>
                <p className="text-blue-700 text-sm">专业的介护技能培训，帮助您在日本介护行业发展</p>
              </div>

              <div
                className="bg-green-50 p-4 rounded-lg border border-green-200 cursor-pointer hover:bg-green-100 transition-colors"
                onClick={() => handleTabChange('nurse')}
              >
                <h3 className="font-semibold text-green-800 mb-2">👩‍⚕️ 赴日护士</h3>
                <p className="text-green-700 text-sm">护士资格认证指导，助您在日本医疗机构工作</p>
              </div>
            </div>

            {/* 通知功能 */}
            <div className="bg-yellow-50 p-4 rounded-lg border border-yellow-200">
              <div className="flex items-start space-x-2">
                <span className="text-yellow-600 text-lg">📢</span>
                <div>
                  <h3 className="font-semibold text-yellow-800 mb-1">最新通知</h3>
                  <p className="text-yellow-700 text-sm">2025-06-18 网站制作中，更多功能即将上线，敬请期待...</p>
                </div>
              </div>
            </div>
          </div>
        );

      case 'care':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">赴日介护</h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">行业前景</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  日本正面临严重的老龄化问题，65岁以上人口占比超过28%。预计到2025年，日本将缺少约34万名介护人员。
                  政府大力推进外国人介护人才引进政策，为海外专业人才提供了广阔的发展机遇。
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">工作内容</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• 协助老年人日常生活护理</li>
                  <li>• 提供身体护理和精神支持</li>
                  <li>• 协助医疗护理工作</li>
                </ul>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">申请条件</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• 年龄18-35岁</li>
                  <li>• 高中以上学历</li>
                  <li>• 身体健康，无传染病史</li>
                  <li>• 具备基本的日语沟通能力(N4及以上)</li>
                </ul>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">薪资待遇</h3>
                <p className="text-gray-600 text-sm">月薪15-25万日元，包含各种津贴和福利</p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">签证种类</h3>
                <p className="text-gray-600 text-sm">特定技能一号签证，专门为介护行业设立的工作签证</p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">收费标准</h3>
                <p className="text-gray-600 text-sm">
                  不成功不收费！学日语、考试、来回机票、房租等费用自费
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">未来方向</h3>
                <p className="text-gray-600 text-sm">
                  介护工作5年后通过考试可转介护签证，之后可满10年永驻或满5年归化
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">申请流程</h3>
                <div className="text-gray-600 text-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">1</span>
                    <span>咨询</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">2</span>
                    <span>性格测试</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">3</span>
                    <span>补全资料</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">4</span>
                    <span>考试准备</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">5</span>
                    <span>医院面试</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">6</span>
                    <span>手续准备</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">7</span>
                    <span>签证申请</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">8</span>
                    <span>来日工作</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  onClick={() => alert('常见问答功能开发中，敬请期待！')}
                >
                  常见问答
                </button>
                <button
                  className="flex-1 bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
                  onClick={() => router.push('/consultation')}
                >
                  我要咨询
                </button>
              </div>
            </div>
          </div>
        );

      case 'nurse':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">赴日护士</h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">行业前景</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  日本医疗行业面临护士人才严重短缺，预计到2025年将缺少约27万名护士。
                  日本政府积极推进外国护士引进政策，为具备专业资质的海外护士提供了优质的就业机会和发展平台。
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">工作内容</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• 医院临床护理工作</li>
                  <li>• 患者护理和健康指导</li>
                  <li>• 协助医生进行医疗操作</li>
                </ul>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">申请条件</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• 护理专业毕业</li>
                  <li>• 持有护士执业资格证</li>
                  <li>• 日语N1考试合格证</li>
                </ul>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">薪资待遇</h3>
                <p className="text-gray-600 text-sm">月薪20-35万日元，享受完善的社会保障</p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">签证种类</h3>
                <p className="text-gray-600 text-sm">
                  医疗签证，需要日语考试和日本护士资格证。可以先留学签证考上了转医疗签证
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">收费标准</h3>
                <p className="text-gray-600 text-sm">
                  不成功不收费！学日语、考试、来回机票、房租等费用自费，需要先留学的学费自费
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">未来方向</h3>
                <p className="text-gray-600 text-sm">
                  医疗签证直接就可以满10年永驻或满5年归化
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">申请流程</h3>
                <div className="text-gray-600 text-sm">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">1</span>
                    <span>咨询</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">2</span>
                    <span>性格测试</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">3</span>
                    <span>补全资料</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">4</span>
                    <span>考试准备</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">5</span>
                    <span>医院面试</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">6</span>
                    <span>手续准备</span>
                  </div>
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">7</span>
                    <span>签证申请</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="bg-green-100 text-green-800 px-2 py-1 rounded text-xs">8</span>
                    <span>来日工作</span>
                  </div>
                </div>
              </div>

              <div className="flex space-x-3">
                <button
                  className="flex-1 bg-blue-600 text-white py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors"
                  onClick={() => alert('常见问答功能开发中，敬请期待！')}
                >
                  常见问答
                </button>
                <button
                  className="flex-1 bg-green-600 text-white py-3 rounded-lg font-medium hover:bg-green-700 transition-colors"
                  onClick={() => router.push('/consultation')}
                >
                  我要咨询
                </button>
              </div>
            </div>
          </div>
        );

      case 'about':
        return (
          <div className="space-y-6">
            <h2 className="text-2xl font-bold text-gray-800">关于我们</h2>
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">公司简介</h3>
                <p className="text-gray-600 text-sm leading-relaxed">
                  我们是一家专业的国际人才交流机构，专注于为中国专业人才提供赴日工作的机会和服务。
                  凭借多年的行业经验和丰富的资源，我们已成功帮助数千名专业人才实现了赴日工作的梦想。
                </p>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">服务优势</h3>
                <ul className="text-gray-600 text-sm space-y-1">
                  <li>• 专业的咨询团队</li>
                  <li>• 完善的培训体系</li>
                  <li>• 全程跟踪服务</li>
                  <li>• 丰富的合作资源</li>
                </ul>
              </div>

              <div className="bg-white p-4 rounded-lg shadow-sm border">
                <h3 className="font-semibold text-gray-800 mb-2">联系方式</h3>
                <div className="text-gray-600 text-sm space-y-1">
                  <p>📞 电话：+81 123-456-7890</p>
                  <p>📧 邮箱：<EMAIL></p>
                  <p>📍 地址：日本东京足立区</p>
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-md mx-auto px-4 py-3 flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center">
              <span className="text-white text-sm font-bold">日</span>
            </div>
            <h1 className="text-lg font-bold text-gray-800">赴日人才交流</h1>
          </div>
          <button className="bg-blue-600 text-white px-4 py-2 rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors">
            登录
          </button>
        </div>
      </header>

      {/* Navigation Tabs */}
      <nav className="bg-white border-b">
        <div className="max-w-md mx-auto px-4">
          <div className="flex">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => handleTabChange(tab.id)}
                className={`flex-1 py-3 text-sm font-medium text-center border-b-2 transition-colors ${
                  activeTab === tab.id
                    ? 'border-blue-600 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700'
                }`}
              >
                {tab.label}
              </button>
            ))}
          </div>
        </div>
      </nav>

      {/* Main Content */}
      <main className="max-w-md mx-auto px-4 py-6">
        {renderContent()}
      </main>
    </div>
  );
}

export default function Home() {
  return (
    <Suspense fallback={
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">加载中...</p>
        </div>
      </div>
    }>
      <HomeContent />
    </Suspense>
  );
}
